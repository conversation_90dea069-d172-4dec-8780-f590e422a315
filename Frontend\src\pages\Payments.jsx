import React, { useState, useEffect } from 'react'
import { paymentsAPI } from '../services/api'
import { 
  Search, 
  Filter, 
  DollarSign, 
  RefreshCw, 
  ChevronLeft, 
  ChevronRight,
  ExternalLink,
  AlertTriangle
} from 'lucide-react'

function Payments() {
  const [payments, setPayments] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [total, setTotal] = useState(0)
  const [showRefundModal, setShowRefundModal] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState(null)
  const [refundData, setRefundData] = useState({ amount: '', reason: '' })

  useEffect(() => {
    fetchPayments()
  }, [currentPage, statusFilter])

  const fetchPayments = async () => {
    try {
      setLoading(true)
      const params = {
        page: currentPage,
        limit: 20,
        ...(statusFilter && { status: statusFilter })
      }
      
      const response = await paymentsAPI.getPayments(params)
      setPayments(response.data.payments)
      setTotalPages(response.data.total_pages)
      setTotal(response.data.total)
    } catch (error) {
      console.error('Error fetching payments:', error)
      setError('Failed to load payments')
    } finally {
      setLoading(false)
    }
  }

  const handleRefund = async (e) => {
    e.preventDefault()
    try {
      await paymentsAPI.processRefund(
        selectedPayment.id,
        parseFloat(refundData.amount),
        refundData.reason
      )
      setShowRefundModal(false)
      setSelectedPayment(null)
      setRefundData({ amount: '', reason: '' })
      await fetchPayments()
    } catch (error) {
      console.error('Error processing refund:', error)
      alert('Failed to process refund')
    }
  }

  const openRefundModal = (payment) => {
    setSelectedPayment(payment)
    setRefundData({
      amount: (payment.amount - payment.refunded_amount).toString(),
      reason: ''
    })
    setShowRefundModal(true)
  }

  const getStatusBadge = (status) => {
    const badges = {
      completed: 'badge-success',
      pending: 'badge-warning',
      failed: 'badge-danger',
      refunded: 'badge-info'
    }
    return badges[status] || 'badge-info'
  }

  const getGatewayBadge = (gateway) => {
    const badges = {
      stripe: 'badge-info',
      razorpay: 'badge-warning',
      sslcommerz: 'badge-success'
    }
    return badges[gateway] || 'badge-info'
  }

  if (loading && payments.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payments</h1>
          <p className="text-gray-600">Manage payment transactions and refunds</p>
        </div>
        <div className="text-sm text-gray-500">
          Total: {total} payments
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-content">
          <div className="flex flex-col sm:flex-row gap-4">
            <select
              className="input"
              value={statusFilter}
              onChange={(e) => {
                setStatusFilter(e.target.value)
                setCurrentPage(1)
              }}
            >
              <option value="">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="refunded">Refunded</option>
            </select>
            
            <button 
              onClick={fetchPayments}
              className="btn-outline"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Payments Table */}
      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">Transaction</th>
                <th className="table-header-cell">User</th>
                <th className="table-header-cell">Amount</th>
                <th className="table-header-cell">Status</th>
                <th className="table-header-cell">Gateway</th>
                <th className="table-header-cell">Date</th>
                <th className="table-header-cell">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {payments.map((payment) => (
                <tr key={payment.id}>
                  <td className="table-cell">
                    <div>
                      <div className="font-medium text-gray-900">
                        #{payment.id}
                      </div>
                      {payment.transaction_id && (
                        <div className="text-sm text-gray-500">
                          {payment.transaction_id}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="table-cell">
                    <div>
                      <div className="font-medium text-gray-900">
                        {payment.user_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {payment.user_email}
                      </div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <div>
                      <div className="font-medium text-gray-900">
                        ${payment.amount} {payment.currency}
                      </div>
                      {payment.refunded_amount > 0 && (
                        <div className="text-sm text-red-600">
                          Refunded: ${payment.refunded_amount}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className={`badge ${getStatusBadge(payment.status)}`}>
                      {payment.status}
                    </span>
                  </td>
                  <td className="table-cell">
                    {payment.gateway && (
                      <span className={`badge ${getGatewayBadge(payment.gateway)}`}>
                        {payment.gateway}
                      </span>
                    )}
                  </td>
                  <td className="table-cell">
                    {new Date(payment.created_at).toLocaleDateString()}
                  </td>
                  <td className="table-cell">
                    {payment.status === 'completed' && 
                     payment.refunded_amount < payment.amount && (
                      <button
                        onClick={() => openRefundModal(payment)}
                        className="btn-outline text-xs"
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Refund
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-3 border-t border-gray-200">
            <div className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="btn-outline p-2 disabled:opacity-50"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="btn-outline p-2 disabled:opacity-50"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Refund Modal */}
      {showRefundModal && selectedPayment && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-6 w-6 text-orange-500 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">
                Process Refund
              </h3>
            </div>
            
            <div className="mb-4 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Payment Details:</p>
              <p className="font-medium">#{selectedPayment.id} - {selectedPayment.user_name}</p>
              <p className="text-sm">
                Amount: ${selectedPayment.amount} {selectedPayment.currency}
              </p>
              <p className="text-sm">
                Available for refund: ${selectedPayment.amount - selectedPayment.refunded_amount}
              </p>
            </div>
            
            <form onSubmit={handleRefund} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Refund Amount
                </label>
                <input
                  type="number"
                  step="0.01"
                  max={selectedPayment.amount - selectedPayment.refunded_amount}
                  required
                  className="input mt-1"
                  value={refundData.amount}
                  onChange={(e) => setRefundData({...refundData, amount: e.target.value})}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Refund Reason
                </label>
                <textarea
                  required
                  rows={3}
                  className="input mt-1"
                  placeholder="Enter reason for refund..."
                  value={refundData.reason}
                  onChange={(e) => setRefundData({...refundData, reason: e.target.value})}
                />
              </div>
              
              <div className="flex space-x-3 pt-4">
                <button type="submit" className="btn-danger flex-1">
                  Process Refund
                </button>
                <button
                  type="button"
                  onClick={() => setShowRefundModal(false)}
                  className="btn-secondary flex-1"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {error && (
        <div className="text-center py-12">
          <p className="text-red-600">{error}</p>
          <button onClick={fetchPayments} className="btn-primary mt-4">
            Retry
          </button>
        </div>
      )}
    </div>
  )
}

export default Payments
