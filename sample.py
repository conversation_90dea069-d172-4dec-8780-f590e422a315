"""
Sample data for GiveJob Admin Panel
This file contains sample admin and customer profiles for testing and development.
Can be easily separated or deleted in production.
"""

from datetime import datetime, date, timedelta
from passlib.context import CryptContext
from model import *
from database import SessionLocal

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password):
    return pwd_context.hash(password)

# Sample Admin Profiles
SAMPLE_ADMINS = [
    {
        "first_name": "Admin",
        "last_name": "User",
        "email": "<EMAIL>",
        "password": get_password_hash("admin123"),
        "role": "admin",
        "status": "active",
        "created_at": datetime.utcnow() - timedelta(days=365)
    },
    {
        "first_name": "Super",
        "last_name": "Admin",
        "email": "<EMAIL>",
        "password": get_password_hash("super123"),
        "role": "admin",
        "status": "active",
        "created_at": datetime.utcnow() - timedelta(days=300)
    },
    {
        "first_name": "Support",
        "last_name": "Manager",
        "email": "<EMAIL>",
        "password": get_password_hash("support123"),
        "role": "admin",
        "status": "active",
        "created_at": datetime.utcnow() - timedelta(days=200)
    }
]

# Sample Customer Profiles
SAMPLE_CUSTOMERS = [
    {
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "password": get_password_hash("password123"),
        "role": "customer",
        "status": "active",
        "available_credits": 50,
        "last_login_at": datetime.utcnow() - timedelta(days=2),
        "created_at": datetime.utcnow() - timedelta(days=90)
    },
    {
        "first_name": "Jane",
        "last_name": "Smith",
        "email": "<EMAIL>",
        "password": get_password_hash("password123"),
        "role": "customer",
        "status": "active",
        "available_credits": 25,
        "last_login_at": datetime.utcnow() - timedelta(days=1),
        "created_at": datetime.utcnow() - timedelta(days=60)
    },
    {
        "first_name": "Mike",
        "last_name": "Johnson",
        "email": "<EMAIL>",
        "password": get_password_hash("password123"),
        "role": "customer",
        "status": "active",
        "available_credits": 100,
        "last_login_at": datetime.utcnow() - timedelta(days=5),
        "created_at": datetime.utcnow() - timedelta(days=120)
    },
    {
        "first_name": "Sarah",
        "last_name": "Wilson",
        "email": "<EMAIL>",
        "password": get_password_hash("password123"),
        "role": "customer",
        "status": "banned",
        "available_credits": 0,
        "banned_reason": "Violation of terms of service",
        "last_login_at": datetime.utcnow() - timedelta(days=30),
        "created_at": datetime.utcnow() - timedelta(days=150)
    },
    {
        "first_name": "David",
        "last_name": "Brown",
        "email": "<EMAIL>",
        "password": get_password_hash("password123"),
        "role": "customer",
        "status": "active",
        "available_credits": 75,
        "last_login_at": datetime.utcnow() - timedelta(hours=6),
        "created_at": datetime.utcnow() - timedelta(days=45)
    },
    {
        "first_name": "Emily",
        "last_name": "Davis",
        "email": "<EMAIL>",
        "password": get_password_hash("password123"),
        "role": "customer",
        "status": "active",
        "available_credits": 30,
        "last_login_at": datetime.utcnow() - timedelta(days=10),
        "created_at": datetime.utcnow() - timedelta(days=75)
    }
]

# Sample Subscription Plans
SAMPLE_PLANS = [
    {
        "name": "Free",
        "price": 0.0,
        "currency": "USD",
        "resume_limit": 1,
        "cover_letter_limit": 3,
        "application_limit": 5,
        "features": {
            "basic_templates": True,
            "ai_suggestions": False,
            "priority_support": False,
            "analytics": False
        },
        "is_active": True
    },
    {
        "name": "Basic",
        "price": 9.99,
        "currency": "USD",
        "resume_limit": 5,
        "cover_letter_limit": 15,
        "application_limit": 25,
        "features": {
            "basic_templates": True,
            "ai_suggestions": True,
            "priority_support": False,
            "analytics": True
        },
        "is_active": True
    },
    {
        "name": "Pro",
        "price": 19.99,
        "currency": "USD",
        "resume_limit": 15,
        "cover_letter_limit": 50,
        "application_limit": 100,
        "features": {
            "basic_templates": True,
            "premium_templates": True,
            "ai_suggestions": True,
            "priority_support": True,
            "analytics": True,
            "custom_branding": True
        },
        "is_active": True
    },
    {
        "name": "Ultimate",
        "price": 39.99,
        "currency": "USD",
        "resume_limit": None,  # Unlimited
        "cover_letter_limit": None,  # Unlimited
        "application_limit": None,  # Unlimited
        "features": {
            "basic_templates": True,
            "premium_templates": True,
            "ai_suggestions": True,
            "priority_support": True,
            "analytics": True,
            "custom_branding": True,
            "api_access": True,
            "white_label": True
        },
        "is_active": True
    }
]

def create_sample_data():
    """
    Create sample data in the database for testing purposes.
    This function should be called only in development environment.
    """
    db = SessionLocal()
    try:
        # Create sample admins
        for admin_data in SAMPLE_ADMINS:
            existing_admin = db.query(User).filter(User.email == admin_data["email"]).first()
            if not existing_admin:
                admin = User(**admin_data)
                db.add(admin)
        
        # Create sample customers
        for customer_data in SAMPLE_CUSTOMERS:
            existing_customer = db.query(User).filter(User.email == customer_data["email"]).first()
            if not existing_customer:
                customer = User(**customer_data)
                db.add(customer)
        
        # Create sample plans
        for plan_data in SAMPLE_PLANS:
            existing_plan = db.query(Plan).filter(Plan.name == plan_data["name"]).first()
            if not existing_plan:
                plan = Plan(**plan_data)
                db.add(plan)
        
        db.commit()
        print("Sample data created successfully!")
        
        # Create sample subscriptions and payments
        create_sample_subscriptions_and_payments(db)
        
    except Exception as e:
        print(f"Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

def create_sample_subscriptions_and_payments(db):
    """Create sample subscriptions and payments for testing"""
    
    # Get some users and plans
    customers = db.query(User).filter(User.role == "customer").limit(4).all()
    plans = db.query(Plan).filter(Plan.name != "Free").all()
    
    if not customers or not plans:
        return
    
    # Create sample subscriptions
    for i, customer in enumerate(customers[:3]):
        plan = plans[i % len(plans)]
        
        subscription = UserSubscription(
            user_id=customer.id,
            plan_id=plan.id,
            start_date=date.today() - timedelta(days=30),
            end_date=date.today() + timedelta(days=30),
            is_active=True,
            resume_count=i + 1,
            cover_letter_count=(i + 1) * 3,
            application_count=(i + 1) * 5
        )
        db.add(subscription)
        
        # Create corresponding payment
        payment = Payment(
            user_id=customer.id,
            plan_id=plan.id,
            transaction_id=f"txn_{customer.id}_{plan.id}_{int(datetime.utcnow().timestamp())}",
            amount=plan.price,
            currency=plan.currency,
            status="completed",
            payment_method="credit_card",
            gateway="stripe",
            created_at=datetime.utcnow() - timedelta(days=30)
        )
        db.add(payment)
    
    # Create some AI usage logs
    for customer in customers:
        for action_type in ["resume", "cover_letter", "apply"]:
            usage_log = AIUsageLog(
                user_id=customer.id,
                action_type=action_type,
                usage_count=1,
                tokens_used=150 + (customer.id * 10),
                estimated_cost=0.002 + (customer.id * 0.001),
                created_at=datetime.utcnow() - timedelta(days=customer.id)
            )
            db.add(usage_log)
    
    db.commit()

if __name__ == "__main__":
    create_sample_data()
