from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Date, Boolean, Float, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
from database import Base, SessionLocal

# Core User Management (Enhanced from your original)
class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    email = Column(String, unique=True, nullable=False)
    password = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    google_oauth_id = Column(String, unique=True, nullable=True)
    email_verified_at = Column(DateTime, nullable=True)
    role = Column(String, nullable=False, default='customer')  # customer, admin, support
    last_login_at = Column(DateTime, nullable=True)
    
    # Admin panel additions
    status = Column(String, default='active')  # active, banned, suspended
    available_credits = Column(Integer, default=0)
    daily_usage_count = Column(Integer, default=0)
    daily_usage_date = Column(Date, nullable=True)
    banned_reason = Column(Text, nullable=True)
    referral_code = Column(String, unique=True, nullable=True)
    referred_by_user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    
    # Relationships
    resumes = relationship('Resume', back_populates='user')
    subscriptions = relationship('UserSubscription', back_populates='user')
    payments = relationship('Payment', back_populates='user')
    job_applications = relationship('JobApplication', back_populates='user')
    ai_usage_logs = relationship('AIUsageLog', back_populates='user')

class Resume(Base):
    __tablename__ = 'resumes'
  
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    resume_data = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    parsed_summary_json = Column(Text, nullable=True)
    version_name = Column(String, nullable=True)
    is_primary = Column(Boolean, default=False)
   
    # Relationships
    user = relationship('User', back_populates='resumes')
    job_applications = relationship('JobApplication', back_populates='original_resume')

# Enhanced Plan (from your original)
class Plan(Base):
    __tablename__ = 'plans'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, unique=True, nullable=False)
    price = Column(Float, nullable=False)  # Changed from String to Float
    currency = Column(String, nullable=False)
    
    # Limits for admin panel
    resume_limit = Column(Integer, nullable=True)
    cover_letter_limit = Column(Integer, nullable=True)
    application_limit = Column(Integer, nullable=True)
    features = Column(JSON, nullable=True)  # Store features as JSON
    
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    subscriptions = relationship('UserSubscription', back_populates='plan')
    payments = relationship('Payment', back_populates='plan')

class UserSubscription(Base):
    __tablename__ = 'user_subscriptions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    plan_id = Column(Integer, ForeignKey('plans.id'), nullable=False)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Usage tracking
    resume_count = Column(Integer, default=0)
    cover_letter_count = Column(Integer, default=0)
    application_count = Column(Integer, default=0)
    
    # Relationships
    user = relationship('User', back_populates='subscriptions')
    plan = relationship('Plan', back_populates='subscriptions')

# Enhanced Payment (from your original)
class Payment(Base):
    __tablename__ = 'payments'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    transaction_id = Column(String, unique=True, nullable=True)
    amount = Column(Float, nullable=False)  # Changed from String to Float
    currency = Column(String, nullable=False)
    status = Column(String, nullable=False)  # pending, completed, failed, refunded
    payment_method = Column(String, nullable=True)
    plan_id = Column(Integer, ForeignKey('plans.id'), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Admin panel additions
    gateway = Column(String, nullable=True)  # stripe, razorpay, sslcommerz
    refunded_amount = Column(Float, default=0.0)
    refund_reason = Column(Text, nullable=True)
    
    # Relationships
    user = relationship('User', back_populates='payments')
    plan = relationship('Plan', back_populates='payments')

class JobApplication(Base):
    __tablename__ = 'job_applications'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    resume_id = Column(Integer, ForeignKey('resumes.id'), nullable=False)
    job_title = Column(String, nullable=False)
    company_name = Column(String, nullable=False)
    application_date = Column(Date, nullable=False)
    status = Column(String, default='applied')
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship('User', back_populates='job_applications')
    original_resume = relationship('Resume', back_populates='job_applications')

# Enhanced AI Usage (from your original)
class AIUsageLog(Base):
    __tablename__ = 'ai_usage_logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    action_type = Column(String, nullable=False)  # resume, cover_letter, apply
    usage_count = Column(Integer, default=1)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Admin panel additions for OpenAI tracking
    tokens_used = Column(Integer, default=0)
    estimated_cost = Column(Float, default=0.0)
    
    # Relationships
    user = relationship('User', back_populates='ai_usage_logs')

# New tables for admin panel features

# Job Sources for admin panel
class JobSource(Base):
    __tablename__ = 'job_sources'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    api_url = Column(String, nullable=True)
    api_key = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    settings = Column(JSON, nullable=True)  # Store scraping/API settings
    created_at = Column(DateTime, default=datetime.utcnow)

# Email templates and settings
class EmailTemplate(Base):
    __tablename__ = 'email_templates'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, unique=True, nullable=False)
    subject = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    template_type = Column(String, nullable=False)  # welcome, subscription, etc.
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

# Affiliate system
class AffiliateCommission(Base):
    __tablename__ = 'affiliate_commissions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    affiliate_user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    referred_user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    payment_id = Column(Integer, ForeignKey('payments.id'), nullable=False)
    commission_amount = Column(Float, nullable=False)
    is_paid = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    affiliate_user = relationship('User', foreign_keys=[affiliate_user_id])
    referred_user = relationship('User', foreign_keys=[referred_user_id])
    payment = relationship('Payment')

# Site settings for admin panel
class SiteSettings(Base):
    __tablename__ = 'site_settings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String, unique=True, nullable=False)
    value = Column(Text, nullable=True)
    category = Column(String, nullable=False)  # general, email, payment, etc.
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Content management
class BlogPost(Base):
    __tablename__ = 'blog_posts'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String, nullable=False)
    slug = Column(String, unique=True, nullable=False)
    content = Column(Text, nullable=False)
    is_published = Column(Boolean, default=False)
    author_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    author = relationship('User')

class FAQ(Base):
    __tablename__ = 'faqs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    question = Column(String, nullable=False)
    answer = Column(Text, nullable=False)
    category = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)

# Analytics summary table (simplified)
class DailyAnalytics(Base):
    __tablename__ = 'daily_analytics'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(Date, unique=True, nullable=False)
    
    # Usage stats
    total_resumes = Column(Integer, default=0)
    total_cover_letters = Column(Integer, default=0)
    total_applications = Column(Integer, default=0)
    
    # Financial stats
    total_revenue = Column(Float, default=0.0)
    total_ai_cost = Column(Float, default=0.0)
    
    # User stats
    new_users = Column(Integer, default=0)
    active_users = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=datetime.utcnow)