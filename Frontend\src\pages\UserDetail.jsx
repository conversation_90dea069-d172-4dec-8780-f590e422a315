import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import { usersAPI } from '../services/api'
import { 
  ArrowLeft, 
  User, 
  Mail, 
  Calendar, 
  Shield, 
  CreditCard, 
  FileText, 
  Briefcase,
  Ban,
  UserCheck,
  RotateCcw,
  UserPlus,
  Plus
} from 'lucide-react'

function UserDetail() {
  const { id } = useParams()
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [actionLoading, setActionLoading] = useState('')

  useEffect(() => {
    fetchUserDetails()
  }, [id])

  const fetchUserDetails = async () => {
    try {
      setLoading(true)
      const response = await usersAPI.getUserDetails(id)
      setUser(response.data)
    } catch (error) {
      console.error('Error fetching user details:', error)
      setError('Failed to load user details')
    } finally {
      setLoading(false)
    }
  }

  const handleAction = async (action, data = {}) => {
    try {
      setActionLoading(action)
      
      switch (action) {
        case 'ban':
          const reason = prompt('Enter ban reason:')
          if (!reason) return
          await usersAPI.banUser(id, reason)
          break
        case 'unban':
          await usersAPI.unbanUser(id)
          break
        case 'reset':
          await usersAPI.resetUsage(id)
          break
        case 'promote':
          if (confirm('Are you sure you want to promote this user to admin?')) {
            await usersAPI.promoteToAdmin(id)
          } else {
            return
          }
          break
        case 'addCredits':
          const credits = prompt('Enter number of credits to add:')
          if (!credits || isNaN(credits)) return
          await usersAPI.addCredits(id, parseInt(credits))
          break
      }
      
      await fetchUserDetails()
    } catch (error) {
      console.error(`Error performing ${action}:`, error)
      alert(`Failed to ${action} user`)
    } finally {
      setActionLoading('')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">{error}</p>
        <button onClick={fetchUserDetails} className="btn-primary mt-4">
          Retry
        </button>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">User not found</p>
        <Link to="/users" className="btn-primary mt-4">
          Back to Users
        </Link>
      </div>
    )
  }

  const getStatusBadge = (status) => {
    const badges = {
      active: 'badge-success',
      banned: 'badge-danger',
      suspended: 'badge-warning'
    }
    return badges[status] || 'badge-info'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/users" className="btn-ghost p-2">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {user.user.first_name} {user.user.last_name}
            </h1>
            <p className="text-gray-600">{user.user.email}</p>
          </div>
        </div>
        <span className={`badge ${getStatusBadge(user.user.status)}`}>
          {user.user.status}
        </span>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Basic Information</h3>
            </div>
            <div className="card-content">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Full Name</p>
                    <p className="font-medium">{user.user.first_name} {user.user.last_name}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{user.user.email}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Joined</p>
                    <p className="font-medium">{new Date(user.user.created_at).toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Last Login</p>
                    <p className="font-medium">
                      {user.user.last_login_at 
                        ? new Date(user.user.last_login_at).toLocaleDateString()
                        : 'Never'
                      }
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <CreditCard className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Available Credits</p>
                    <p className="font-medium">{user.user.available_credits}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Usage Statistics */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Usage Statistics</h3>
            </div>
            <div className="card-content">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-3">
                  <FileText className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-500">CVs Generated</p>
                    <p className="text-2xl font-bold">{user.statistics.resume_count}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <FileText className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-500">Cover Letters</p>
                    <p className="text-2xl font-bold">{user.statistics.cover_letter_count}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Briefcase className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-500">Job Searches</p>
                    <p className="text-2xl font-bold">{user.statistics.job_search_count}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Subscription Details */}
          {user.subscription && (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Subscription Details</h3>
              </div>
              <div className="card-content">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Plan</p>
                    <p className="font-medium">{user.subscription.plan_name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <span className="badge badge-success">Active</span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Start Date</p>
                    <p className="font-medium">{new Date(user.subscription.start_date).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">End Date</p>
                    <p className="font-medium">{new Date(user.subscription.end_date).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Resume Usage</p>
                    <p className="font-medium">{user.subscription.resume_count}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Cover Letter Usage</p>
                    <p className="font-medium">{user.subscription.cover_letter_count}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="space-y-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Actions</h3>
            </div>
            <div className="card-content space-y-3">
              {user.user.status === 'active' ? (
                <button
                  onClick={() => handleAction('ban')}
                  disabled={actionLoading === 'ban'}
                  className="btn-danger w-full"
                >
                  <Ban className="h-4 w-4 mr-2" />
                  {actionLoading === 'ban' ? 'Banning...' : 'Ban User'}
                </button>
              ) : (
                <button
                  onClick={() => handleAction('unban')}
                  disabled={actionLoading === 'unban'}
                  className="btn-primary w-full"
                >
                  <UserCheck className="h-4 w-4 mr-2" />
                  {actionLoading === 'unban' ? 'Unbanning...' : 'Unban User'}
                </button>
              )}
              
              <button
                onClick={() => handleAction('reset')}
                disabled={actionLoading === 'reset'}
                className="btn-secondary w-full"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                {actionLoading === 'reset' ? 'Resetting...' : 'Reset Usage Limits'}
              </button>
              
              <button
                onClick={() => handleAction('promote')}
                disabled={actionLoading === 'promote'}
                className="btn-outline w-full"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                {actionLoading === 'promote' ? 'Promoting...' : 'Promote to Admin'}
              </button>
              
              <button
                onClick={() => handleAction('addCredits')}
                disabled={actionLoading === 'addCredits'}
                className="btn-ghost w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                {actionLoading === 'addCredits' ? 'Adding...' : 'Add Credits'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserDetail
