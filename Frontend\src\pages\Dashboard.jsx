import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { dashboardAPI } from '../services/api'
import { Users, UserCheck, CreditCard, DollarSign, TrendingUp, Clock } from 'lucide-react'

function Dashboard() {
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      setLoading(true)
      const response = await dashboardAPI.getStats()
      setStats(response.data)
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      setError('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">{error}</p>
        <button 
          onClick={fetchDashboardStats}
          className="btn-primary mt-4"
        >
          Retry
        </button>
      </div>
    )
  }

  const statCards = [
    {
      title: 'Total Users',
      value: stats?.total_users || 0,
      icon: Users,
      color: 'bg-blue-500',
      link: '/users'
    },
    {
      title: 'Active Users',
      value: stats?.active_users || 0,
      icon: UserCheck,
      color: 'bg-green-500',
      link: '/users?status=active'
    },
    {
      title: 'Active Subscriptions',
      value: stats?.active_subscription_users || 0,
      icon: CreditCard,
      color: 'bg-purple-500',
      link: '/subscription-plans'
    },
    {
      title: 'Total Revenue',
      value: `$${(stats?.total_revenue || 0).toLocaleString()}`,
      icon: DollarSign,
      color: 'bg-pink-500',
      link: '/payments'
    },
    {
      title: 'AI Usage',
      value: stats?.total_ai_usage || 0,
      icon: TrendingUp,
      color: 'bg-orange-500'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {statCards.map((stat, index) => (
          <div key={index} className="stat-card">
            <div className="flex items-center">
              <div className={`p-3 rounded-lg ${stat.color}`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="stat-label">{stat.title}</p>
                <p className="stat-value">{stat.value}</p>
              </div>
            </div>
            {stat.link && (
              <div className="mt-4">
                <Link 
                  to={stat.link}
                  className="text-sm text-pink-600 hover:text-pink-800 font-medium"
                >
                  View details →
                </Link>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Users */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Recent Users</h3>
          </div>
          <div className="card-content">
            {stats?.recent_users?.length > 0 ? (
              <div className="space-y-4">
                {stats.recent_users.map((user) => (
                  <div key={user.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">
                        {user.first_name} {user.last_name}
                      </p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                    <div className="text-right">
                      <span className={`badge ${
                        user.status === 'active' ? 'badge-success' : 
                        user.status === 'banned' ? 'badge-danger' : 'badge-warning'
                      }`}>
                        {user.status}
                      </span>
                      <p className="text-xs text-gray-500 mt-1">
                        <Clock className="h-3 w-3 inline mr-1" />
                        {new Date(user.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
                <div className="pt-4 border-t">
                  <Link 
                    to="/users" 
                    className="text-sm text-pink-600 hover:text-pink-800 font-medium"
                  >
                    View all users →
                  </Link>
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No recent users</p>
            )}
          </div>
        </div>

        {/* Recent Payments */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Recent Payments</h3>
          </div>
          <div className="card-content">
            {stats?.recent_payments?.length > 0 ? (
              <div className="space-y-4">
                {stats.recent_payments.map((payment) => (
                  <div key={payment.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">{payment.user_name}</p>
                      <p className="text-sm text-gray-500">
                        ${payment.amount} {payment.currency}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`badge ${
                        payment.status === 'completed' ? 'badge-success' : 
                        payment.status === 'failed' ? 'badge-danger' : 'badge-warning'
                      }`}>
                        {payment.status}
                      </span>
                      <p className="text-xs text-gray-500 mt-1">
                        <Clock className="h-3 w-3 inline mr-1" />
                        {new Date(payment.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
                <div className="pt-4 border-t">
                  <Link 
                    to="/payments" 
                    className="text-sm text-pink-600 hover:text-pink-800 font-medium"
                  >
                    View all payments →
                  </Link>
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No recent payments</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
