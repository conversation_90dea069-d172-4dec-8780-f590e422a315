from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, Form
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime, timedelta, date
from typing import Optional, List
import jwt
from passlib.context import CryptContext
import uvicorn
from pydantic import BaseModel


# Import models and database
from model import *
from database import get_db, create_tables

# Create FastAPI app
app = FastAPI(
    title="GiveJob Admin Panel API",
    description="Admin panel backend for GiveJob platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
SECRET_KEY = "your-secret-key-here"  # Change this in production
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Create static directory for assets (if needed)
# app.mount("/static", StaticFiles(directory="static"), name="static")

# Pydantic models for API
class LoginRequest(BaseModel):
    email: str
    password: str

class UserResponse(BaseModel):
    id: int
    first_name: str
    last_name: str
    email: str
    status: str
    role: str
    created_at: datetime
    last_login_at: Optional[datetime]

class DashboardStats(BaseModel):
    total_users: int
    active_users: int
    active_subscription_users: int
    total_revenue: float
    total_ai_usage: int
    recent_users: List[UserResponse]
    recent_payments: List[dict]

class PlanCreate(BaseModel):
    name: str
    price: float
    currency: str
    resume_limit: Optional[int]
    cover_letter_limit: Optional[int]
    application_limit: Optional[int]
    features: Optional[dict]

class PlanUpdate(BaseModel):
    name: Optional[str]
    price: Optional[float]
    currency: Optional[str]
    resume_limit: Optional[int]
    cover_letter_limit: Optional[int]
    application_limit: Optional[int]
    features: Optional[dict]
    is_active: Optional[bool]

# Utility functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return email
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

def get_current_admin_user(email: str = Depends(verify_token), db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == email, User.role == "admin").first()
    if not user:
        raise HTTPException(status_code=403, detail="Admin access required")
    return user

# Authentication endpoints
@app.post("/admin-log-in")
async def admin_login(request: LoginRequest, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == request.email).first()

    if not user or not verify_password(request.password, user.password) or user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Email or password not matched."
        )

    # Update last login
    user.last_login_at = datetime.utcnow()
    db.commit()

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": user.role
        }
    }

# Dashboard endpoint
@app.get("/dashboard", response_model=DashboardStats)
async def get_dashboard_stats(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    # Total users
    total_users = db.query(User).filter(User.role == "customer").count()

    # Active users (logged in within last month)
    one_month_ago = datetime.utcnow() - timedelta(days=30)
    active_users = db.query(User).filter(
        User.role == "customer",
        User.last_login_at >= one_month_ago
    ).count()

    # Active subscription users
    active_subscription_users = db.query(User).join(UserSubscription).filter(
        User.role == "customer",
        UserSubscription.is_active == True,
        UserSubscription.end_date >= date.today()
    ).count()

    # Total revenue
    total_revenue = db.query(func.sum(Payment.amount)).filter(
        Payment.status == "completed"
    ).scalar() or 0.0

    # Total AI usage
    total_ai_usage = db.query(func.sum(AIUsageLog.usage_count)).scalar() or 0

    # Recent users (last 10)
    recent_users = db.query(User).filter(User.role == "customer").order_by(
        User.created_at.desc()
    ).limit(10).all()

    # Recent payments (last 10)
    recent_payments_query = db.query(Payment).join(User).order_by(
        Payment.created_at.desc()
    ).limit(10).all()

    recent_payments = []
    for payment in recent_payments_query:
        recent_payments.append({
            "id": payment.id,
            "user_name": f"{payment.user.first_name} {payment.user.last_name}",
            "amount": payment.amount,
            "currency": payment.currency,
            "status": payment.status,
            "created_at": payment.created_at
        })

    return DashboardStats(
        total_users=total_users,
        active_users=active_users,
        active_subscription_users=active_subscription_users,
        total_revenue=total_revenue,
        total_ai_usage=total_ai_usage,
        recent_users=recent_users,
        recent_payments=recent_payments
    )

# User management endpoints
@app.get("/users")
async def get_users(
    page: int = 1,
    limit: int = 20,
    search: Optional[str] = None,
    subscription_type: Optional[str] = None,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    query = db.query(User).filter(User.role == "customer")

    # Apply filters
    if search:
        query = query.filter(User.email.contains(search))

    if status:
        query = query.filter(User.status == status)

    if subscription_type:
        query = query.join(UserSubscription).join(Plan).filter(
            Plan.name == subscription_type,
            UserSubscription.is_active == True
        )

    # Pagination
    offset = (page - 1) * limit
    users = query.offset(offset).limit(limit).all()
    total = query.count()

    # Get subscription info for each user
    users_with_subscription = []
    for user in users:
        active_subscription = db.query(UserSubscription).join(Plan).filter(
            UserSubscription.user_id == user.id,
            UserSubscription.is_active == True
        ).first()

        subscription_type = "Free"
        if active_subscription:
            subscription_type = active_subscription.plan.name

        users_with_subscription.append({
            "id": user.id,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email": user.email,
            "status": user.status,
            "subscription_type": subscription_type,
            "created_at": user.created_at,
            "last_login_at": user.last_login_at
        })

    return {
        "users": users_with_subscription,
        "total": total,
        "page": page,
        "limit": limit,
        "total_pages": (total + limit - 1) // limit
    }

@app.get("/user/{user_id}")
async def get_user_details(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.id == user_id, User.role == "customer").first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get user statistics
    resume_count = db.query(Resume).filter(Resume.user_id == user_id).count()
    cover_letter_count = db.query(AIUsageLog).filter(
        AIUsageLog.user_id == user_id,
        AIUsageLog.action_type == "cover_letter"
    ).count()
    job_search_count = db.query(JobApplication).filter(
        JobApplication.user_id == user_id
    ).count()

    # Get active subscription
    active_subscription = db.query(UserSubscription).join(Plan).filter(
        UserSubscription.user_id == user_id,
        UserSubscription.is_active == True
    ).first()

    subscription_info = None
    if active_subscription:
        subscription_info = {
            "plan_name": active_subscription.plan.name,
            "start_date": active_subscription.start_date,
            "end_date": active_subscription.end_date,
            "resume_count": active_subscription.resume_count,
            "cover_letter_count": active_subscription.cover_letter_count,
            "application_count": active_subscription.application_count
        }

    return {
        "user": {
            "id": user.id,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email": user.email,
            "status": user.status,
            "available_credits": user.available_credits,
            "created_at": user.created_at,
            "last_login_at": user.last_login_at
        },
        "statistics": {
            "resume_count": resume_count,
            "cover_letter_count": cover_letter_count,
            "job_search_count": job_search_count
        },
        "subscription": subscription_info
    }

@app.post("/user/{user_id}/ban")
async def ban_user(
    user_id: int,
    reason: str = Form(...),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.id == user_id, User.role == "customer").first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user.status = "banned"
    user.banned_reason = reason
    db.commit()

    return {"message": "User banned successfully"}

@app.post("/user/{user_id}/unban")
async def unban_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.id == user_id, User.role == "customer").first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user.status = "active"
    user.banned_reason = None
    db.commit()

    return {"message": "User unbanned successfully"}

@app.post("/user/{user_id}/reset-usage")
async def reset_user_usage(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.id == user_id, User.role == "customer").first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Reset daily usage
    user.daily_usage_count = 0
    user.daily_usage_date = None

    # Reset subscription usage
    active_subscription = db.query(UserSubscription).filter(
        UserSubscription.user_id == user_id,
        UserSubscription.is_active == True
    ).first()

    if active_subscription:
        active_subscription.resume_count = 0
        active_subscription.cover_letter_count = 0
        active_subscription.application_count = 0

    db.commit()

    return {"message": "User usage limits reset successfully"}

@app.post("/user/{user_id}/promote-admin")
async def promote_to_admin(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.id == user_id, User.role == "customer").first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user.role = "admin"
    db.commit()

    return {"message": "User promoted to admin successfully"}

# Subscription plan management
@app.get("/subscription-plans")
async def get_subscription_plans(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    plans = db.query(Plan).all()
    return {"plans": plans}

@app.post("/subscription-plans")
async def create_subscription_plan(
    plan_data: PlanCreate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    # Check if plan name already exists
    existing_plan = db.query(Plan).filter(Plan.name == plan_data.name).first()
    if existing_plan:
        raise HTTPException(status_code=400, detail="Plan name already exists")

    new_plan = Plan(
        name=plan_data.name,
        price=plan_data.price,
        currency=plan_data.currency,
        resume_limit=plan_data.resume_limit,
        cover_letter_limit=plan_data.cover_letter_limit,
        application_limit=plan_data.application_limit,
        features=plan_data.features
    )

    db.add(new_plan)
    db.commit()
    db.refresh(new_plan)

    return {"message": "Plan created successfully", "plan": new_plan}

@app.put("/subscription-plans/{plan_id}")
async def update_subscription_plan(
    plan_id: int,
    plan_data: PlanUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")

    # Update fields if provided
    for field, value in plan_data.dict(exclude_unset=True).items():
        setattr(plan, field, value)

    plan.updated_at = datetime.utcnow()
    db.commit()

    return {"message": "Plan updated successfully", "plan": plan}

@app.delete("/subscription-plans/{plan_id}")
async def delete_subscription_plan(
    plan_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")

    # Check if plan has active subscriptions
    active_subscriptions = db.query(UserSubscription).filter(
        UserSubscription.plan_id == plan_id,
        UserSubscription.is_active == True
    ).count()

    if active_subscriptions > 0:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete plan with active subscriptions"
        )

    db.delete(plan)
    db.commit()

    return {"message": "Plan deleted successfully"}

# Payment management
@app.get("/payments")
async def get_payments(
    page: int = 1,
    limit: int = 20,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    query = db.query(Payment).join(User)

    if status:
        query = query.filter(Payment.status == status)

    offset = (page - 1) * limit
    payments = query.order_by(Payment.created_at.desc()).offset(offset).limit(limit).all()
    total = query.count()

    payments_data = []
    for payment in payments:
        payments_data.append({
            "id": payment.id,
            "transaction_id": payment.transaction_id,
            "user_name": f"{payment.user.first_name} {payment.user.last_name}",
            "user_email": payment.user.email,
            "amount": payment.amount,
            "currency": payment.currency,
            "status": payment.status,
            "payment_method": payment.payment_method,
            "gateway": payment.gateway,
            "created_at": payment.created_at,
            "refunded_amount": payment.refunded_amount
        })

    return {
        "payments": payments_data,
        "total": total,
        "page": page,
        "limit": limit,
        "total_pages": (total + limit - 1) // limit
    }

@app.post("/payments/{payment_id}/refund")
async def process_refund(
    payment_id: int,
    refund_amount: float = Form(...),
    refund_reason: str = Form(...),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    payment = db.query(Payment).filter(Payment.id == payment_id).first()
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")

    if payment.status != "completed":
        raise HTTPException(status_code=400, detail="Can only refund completed payments")

    if refund_amount > payment.amount - payment.refunded_amount:
        raise HTTPException(status_code=400, detail="Refund amount exceeds available amount")

    payment.refunded_amount += refund_amount
    payment.refund_reason = refund_reason

    if payment.refunded_amount >= payment.amount:
        payment.status = "refunded"

    db.commit()

    return {"message": "Refund processed successfully"}

@app.post("/users/{user_id}/add-credits")
async def add_credits_to_user(
    user_id: int,
    credits: int = Form(...),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.id == user_id, User.role == "customer").first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user.available_credits += credits
    db.commit()

    return {"message": f"Added {credits} credits to user successfully"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "GiveJob Admin Panel API is running"}

# Initialize database
@app.on_event("startup")
async def startup_event():
    create_tables()

    # Create sample data for development
    try:
        from sample import create_sample_data
        create_sample_data()
        print("Sample data initialized successfully!")
    except Exception as e:
        print(f"Error initializing sample data: {e}")
        # Fallback to creating just admin user
        db = SessionLocal()
        try:
            admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
            if not admin_user:
                hashed_password = get_password_hash("admin123")
                admin_user = User(
                    first_name="Admin",
                    last_name="User",
                    email="<EMAIL>",
                    password=hashed_password,
                    role="admin",
                    status="active"
                )
                db.add(admin_user)
                db.commit()
                print("Default admin user created: <EMAIL> / admin123")
        finally:
            db.close()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)