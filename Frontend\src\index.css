@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }
  
  body {
    margin: 0;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f8fafc;
    color: #1e293b;
  }
  
  #root {
    min-height: 100vh;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-pink-500 text-white hover:bg-pink-600 h-10 py-2 px-4;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 h-10 py-2 px-4;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 hover:bg-gray-50 h-10 py-2 px-4;
  }
  
  .btn-ghost {
    @apply btn hover:bg-gray-100 h-10 py-2 px-4;
  }
  
  .btn-danger {
    @apply btn bg-red-500 text-white hover:bg-red-600 h-10 py-2 px-4;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-xl font-semibold leading-none tracking-tight;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .sidebar-link {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors;
  }
  
  .sidebar-link-active {
    @apply sidebar-link bg-pink-100 text-pink-700;
  }
  
  .sidebar-link-inactive {
    @apply sidebar-link text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
  
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }
  
  .badge-danger {
    @apply badge bg-red-100 text-red-800;
  }
  
  .badge-info {
    @apply badge bg-blue-100 text-blue-800;
  }
  
  .stat-card {
    @apply card p-6;
  }
  
  .stat-value {
    @apply text-2xl font-bold text-gray-900;
  }
  
  .stat-label {
    @apply text-sm font-medium text-gray-500;
  }
  
  .stat-change {
    @apply text-sm font-medium;
  }
  
  .stat-change-positive {
    @apply stat-change text-green-600;
  }
  
  .stat-change-negative {
    @apply stat-change text-red-600;
  }
}
