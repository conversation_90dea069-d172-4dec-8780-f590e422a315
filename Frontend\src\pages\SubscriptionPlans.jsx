import React, { useState, useEffect } from 'react'
import { plansAPI } from '../services/api'
import { Plus, Edit, Trash2, DollarSign, Users, FileText, Check, X } from 'lucide-react'

function SubscriptionPlans() {
  const [plans, setPlans] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [editingPlan, setEditingPlan] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    price: '',
    currency: 'USD',
    resume_limit: '',
    cover_letter_limit: '',
    application_limit: '',
    features: {}
  })

  useEffect(() => {
    fetchPlans()
  }, [])

  const fetchPlans = async () => {
    try {
      setLoading(true)
      const response = await plansAPI.getPlans()
      setPlans(response.data.plans)
    } catch (error) {
      console.error('Error fetching plans:', error)
      setError('Failed to load subscription plans')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      const planData = {
        ...formData,
        price: parseFloat(formData.price),
        resume_limit: formData.resume_limit ? parseInt(formData.resume_limit) : null,
        cover_letter_limit: formData.cover_letter_limit ? parseInt(formData.cover_letter_limit) : null,
        application_limit: formData.application_limit ? parseInt(formData.application_limit) : null,
      }

      if (editingPlan) {
        await plansAPI.updatePlan(editingPlan.id, planData)
      } else {
        await plansAPI.createPlan(planData)
      }

      setShowModal(false)
      setEditingPlan(null)
      resetForm()
      await fetchPlans()
    } catch (error) {
      console.error('Error saving plan:', error)
      alert('Failed to save plan')
    }
  }

  const handleEdit = (plan) => {
    setEditingPlan(plan)
    setFormData({
      name: plan.name,
      price: plan.price.toString(),
      currency: plan.currency,
      resume_limit: plan.resume_limit?.toString() || '',
      cover_letter_limit: plan.cover_letter_limit?.toString() || '',
      application_limit: plan.application_limit?.toString() || '',
      features: plan.features || {}
    })
    setShowModal(true)
  }

  const handleDelete = async (plan) => {
    if (!confirm(`Are you sure you want to delete the ${plan.name} plan?`)) {
      return
    }

    try {
      await plansAPI.deletePlan(plan.id)
      await fetchPlans()
    } catch (error) {
      console.error('Error deleting plan:', error)
      alert('Failed to delete plan')
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      price: '',
      currency: 'USD',
      resume_limit: '',
      cover_letter_limit: '',
      application_limit: '',
      features: {}
    })
  }

  const openCreateModal = () => {
    setEditingPlan(null)
    resetForm()
    setShowModal(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Subscription Plans</h1>
          <p className="text-gray-600">Manage subscription plans and pricing</p>
        </div>
        <button onClick={openCreateModal} className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          Add Plan
        </button>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {plans.map((plan) => (
          <div key={plan.id} className="card">
            <div className="card-header">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="card-title">{plan.name}</h3>
                  <div className="flex items-center mt-2">
                    <DollarSign className="h-5 w-5 text-green-500" />
                    <span className="text-2xl font-bold text-gray-900">
                      {plan.price}
                    </span>
                    <span className="text-gray-500 ml-1">/{plan.currency}</span>
                  </div>
                </div>
                <span className={`badge ${plan.is_active ? 'badge-success' : 'badge-danger'}`}>
                  {plan.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
            <div className="card-content">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Resume Limit</span>
                  <span className="font-medium">
                    {plan.resume_limit || 'Unlimited'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Cover Letters</span>
                  <span className="font-medium">
                    {plan.cover_letter_limit || 'Unlimited'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Applications</span>
                  <span className="font-medium">
                    {plan.application_limit || 'Unlimited'}
                  </span>
                </div>
                
                {plan.features && Object.keys(plan.features).length > 0 && (
                  <div className="pt-3 border-t">
                    <p className="text-sm font-medium text-gray-700 mb-2">Features:</p>
                    <div className="space-y-1">
                      {Object.entries(plan.features).map(([key, value]) => (
                        <div key={key} className="flex items-center text-sm">
                          {value ? (
                            <Check className="h-3 w-3 text-green-500 mr-2" />
                          ) : (
                            <X className="h-3 w-3 text-red-500 mr-2" />
                          )}
                          <span className="capitalize">{key.replace(/_/g, ' ')}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <div className="flex space-x-2 mt-4 pt-4 border-t">
                <button
                  onClick={() => handleEdit(plan)}
                  className="btn-outline flex-1"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </button>
                <button
                  onClick={() => handleDelete(plan)}
                  className="btn-danger flex-1"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {editingPlan ? 'Edit Plan' : 'Create New Plan'}
            </h3>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Plan Name</label>
                <input
                  type="text"
                  required
                  className="input mt-1"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Price</label>
                  <input
                    type="number"
                    step="0.01"
                    required
                    className="input mt-1"
                    value={formData.price}
                    onChange={(e) => setFormData({...formData, price: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Currency</label>
                  <select
                    className="input mt-1"
                    value={formData.currency}
                    onChange={(e) => setFormData({...formData, currency: e.target.value})}
                  >
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Resume Limit</label>
                <input
                  type="number"
                  className="input mt-1"
                  placeholder="Leave empty for unlimited"
                  value={formData.resume_limit}
                  onChange={(e) => setFormData({...formData, resume_limit: e.target.value})}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Cover Letter Limit</label>
                <input
                  type="number"
                  className="input mt-1"
                  placeholder="Leave empty for unlimited"
                  value={formData.cover_letter_limit}
                  onChange={(e) => setFormData({...formData, cover_letter_limit: e.target.value})}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Application Limit</label>
                <input
                  type="number"
                  className="input mt-1"
                  placeholder="Leave empty for unlimited"
                  value={formData.application_limit}
                  onChange={(e) => setFormData({...formData, application_limit: e.target.value})}
                />
              </div>
              
              <div className="flex space-x-3 pt-4">
                <button type="submit" className="btn-primary flex-1">
                  {editingPlan ? 'Update Plan' : 'Create Plan'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="btn-secondary flex-1"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {error && (
        <div className="text-center py-12">
          <p className="text-red-600">{error}</p>
          <button onClick={fetchPlans} className="btn-primary mt-4">
            Retry
          </button>
        </div>
      )}
    </div>
  )
}

export default SubscriptionPlans
