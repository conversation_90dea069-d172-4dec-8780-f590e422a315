import React, { useState, useEffect } from 'react'
import { Link, useSearchParams } from 'react-router-dom'
import { usersAPI } from '../services/api'
import { Search, Filter, Eye, ChevronLeft, ChevronRight } from 'lucide-react'

function Users() {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchParams, setSearchParams] = useSearchParams()
  
  // Filters and pagination
  const [search, setSearch] = useState(searchParams.get('search') || '')
  const [statusFilter, setStatusFilter] = useState(searchParams.get('status') || '')
  const [subscriptionFilter, setSubscriptionFilter] = useState(searchParams.get('subscription') || '')
  const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get('page')) || 1)
  const [totalPages, setTotalPages] = useState(1)
  const [total, setTotal] = useState(0)

  useEffect(() => {
    fetchUsers()
  }, [currentPage, search, statusFilter, subscriptionFilter])

  useEffect(() => {
    // Update URL params
    const params = new URLSearchParams()
    if (search) params.set('search', search)
    if (statusFilter) params.set('status', statusFilter)
    if (subscriptionFilter) params.set('subscription', subscriptionFilter)
    if (currentPage > 1) params.set('page', currentPage.toString())
    setSearchParams(params)
  }, [search, statusFilter, subscriptionFilter, currentPage, setSearchParams])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const params = {
        page: currentPage,
        limit: 20,
        ...(search && { search }),
        ...(statusFilter && { status: statusFilter }),
        ...(subscriptionFilter && { subscription_type: subscriptionFilter })
      }
      
      const response = await usersAPI.getUsers(params)
      setUsers(response.data.users)
      setTotalPages(response.data.total_pages)
      setTotal(response.data.total)
    } catch (error) {
      console.error('Error fetching users:', error)
      setError('Failed to load users')
    } finally {
      setLoading(false)
    }
  }

  const handleSearchSubmit = (e) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchUsers()
  }

  const handleFilterChange = (filterType, value) => {
    setCurrentPage(1)
    if (filterType === 'status') {
      setStatusFilter(value)
    } else if (filterType === 'subscription') {
      setSubscriptionFilter(value)
    }
  }

  const getStatusBadge = (status) => {
    const badges = {
      active: 'badge-success',
      banned: 'badge-danger',
      suspended: 'badge-warning'
    }
    return badges[status] || 'badge-info'
  }

  const getSubscriptionBadge = (subscription) => {
    const badges = {
      Free: 'badge-info',
      Basic: 'badge-warning',
      Pro: 'badge-success',
      Ultimate: 'badge-danger'
    }
    return badges[subscription] || 'badge-info'
  }

  if (loading && users.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Users</h1>
          <p className="text-gray-600">Manage user accounts and permissions</p>
        </div>
        <div className="text-sm text-gray-500">
          Total: {total} users
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-content">
          <form onSubmit={handleSearchSubmit} className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by email..."
                  className="input pl-10"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </div>
            </div>
            
            <select
              className="input"
              value={statusFilter}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="banned">Banned</option>
              <option value="suspended">Suspended</option>
            </select>
            
            <select
              className="input"
              value={subscriptionFilter}
              onChange={(e) => handleFilterChange('subscription', e.target.value)}
            >
              <option value="">All Subscriptions</option>
              <option value="Free">Free</option>
              <option value="Basic">Basic</option>
              <option value="Pro">Pro</option>
              <option value="Ultimate">Ultimate</option>
            </select>
            
            <button type="submit" className="btn-primary">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </button>
          </form>
        </div>
      </div>

      {/* Users Table */}
      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">User</th>
                <th className="table-header-cell">Status</th>
                <th className="table-header-cell">Subscription</th>
                <th className="table-header-cell">Last Login</th>
                <th className="table-header-cell">Joined</th>
                <th className="table-header-cell">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {users.map((user) => (
                <tr key={user.id}>
                  <td className="table-cell">
                    <div>
                      <div className="font-medium text-gray-900">
                        {user.first_name} {user.last_name}
                      </div>
                      <div className="text-gray-500">{user.email}</div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className={`badge ${getStatusBadge(user.status)}`}>
                      {user.status}
                    </span>
                  </td>
                  <td className="table-cell">
                    <span className={`badge ${getSubscriptionBadge(user.subscription_type)}`}>
                      {user.subscription_type}
                    </span>
                  </td>
                  <td className="table-cell">
                    {user.last_login_at ? (
                      <span className="text-gray-900">
                        {new Date(user.last_login_at).toLocaleDateString()}
                      </span>
                    ) : (
                      <span className="text-gray-500">Never</span>
                    )}
                  </td>
                  <td className="table-cell">
                    {new Date(user.created_at).toLocaleDateString()}
                  </td>
                  <td className="table-cell">
                    <Link
                      to={`/user/${user.id}`}
                      className="btn-ghost p-2"
                      title="View Details"
                    >
                      <Eye className="h-4 w-4" />
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-3 border-t border-gray-200">
            <div className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="btn-outline p-2 disabled:opacity-50"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="btn-outline p-2 disabled:opacity-50"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="text-center py-12">
          <p className="text-red-600">{error}</p>
          <button onClick={fetchUsers} className="btn-primary mt-4">
            Retry
          </button>
        </div>
      )}
    </div>
  )
}

export default Users
