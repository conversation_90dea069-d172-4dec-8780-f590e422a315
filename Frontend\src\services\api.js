import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000'

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// API methods
export const authAPI = {
  login: (email, password) => api.post('/admin-log-in', { email, password }),
}

export const dashboardAPI = {
  getStats: () => api.get('/dashboard'),
}

export const usersAPI = {
  getUsers: (params) => api.get('/users', { params }),
  getUserDetails: (id) => api.get(`/user/${id}`),
  banUser: (id, reason) => api.post(`/user/${id}/ban`, { reason }),
  unbanUser: (id) => api.post(`/user/${id}/unban`),
  resetUsage: (id) => api.post(`/user/${id}/reset-usage`),
  promoteToAdmin: (id) => api.post(`/user/${id}/promote-admin`),
  addCredits: (id, credits) => api.post(`/users/${id}/add-credits`, { credits }),
}

export const plansAPI = {
  getPlans: () => api.get('/subscription-plans'),
  createPlan: (data) => api.post('/subscription-plans', data),
  updatePlan: (id, data) => api.put(`/subscription-plans/${id}`, data),
  deletePlan: (id) => api.delete(`/subscription-plans/${id}`),
}

export const paymentsAPI = {
  getPayments: (params) => api.get('/payments', { params }),
  processRefund: (id, amount, reason) => api.post(`/payments/${id}/refund`, { 
    refund_amount: amount, 
    refund_reason: reason 
  }),
}

export default api
