{"name": "givejob-admin-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "axios": "^1.3.4", "lucide-react": "^0.321.0", "@headlessui/react": "^1.7.13", "clsx": "^1.2.1"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "vite": "^7.0.0", "tailwindcss": "^3.2.7", "autoprefixer": "^10.4.14", "postcss": "^8.4.21"}}